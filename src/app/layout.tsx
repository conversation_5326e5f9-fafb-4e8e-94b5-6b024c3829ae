import type { Metada<PERSON> } from "next";
import { Toaster, ToasterProvider } from "@/components/Toaster";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import "./globals.css";

const queryClient = new QueryClient();

export const metadata: Metadata = {
  title: "Admin Panel",
  description: "Generated by Next.js",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <QueryClientProvider client={queryClient}>
      <ToasterProvider>
        <html lang="en">
          <body>
            {children}
            <Toaster />
          </body>
        </html>
      </ToasterProvider>
    </QueryClientProvider>
  );
}
