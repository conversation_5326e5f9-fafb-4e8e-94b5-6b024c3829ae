import type { Metada<PERSON> } from "next";
import { Toaster } from "@/components/Toaster";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

const queryClient = new QueryClient();

export const metadata: Metadata = {
  title: "Admin Panel",
  description: "Generated by Next.js",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <QueryClientProvider client={queryClient}>
      <Toaster.Provider>
        <html lang="en">{children}</html>
      </Toaster.Provider>
    </QueryClientProvider>
  );
}
