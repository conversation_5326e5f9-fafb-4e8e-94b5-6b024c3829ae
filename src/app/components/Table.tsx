import { FiChevronLeft, FiChevronRight } from "react-icons/fi";
import { memo } from "react";

interface TableData {
  id: number;
  name: string;
  email: string;
  status: string;
}

interface TableColumn {
  key: keyof TableData;
  label: string;
}

interface TableProps {
  data: TableData[];
  columns: TableColumn[];
  pagination?: boolean;
  currentPage?: number;
  totalPages?: number;
  onPageChange?: (page: number) => void;
}

export const Table = memo(
  ({
    data,
    columns,
    pagination = false,
    currentPage = 1,
    totalPages = 1,
    onPageChange,
  }: TableProps) => {
    return (
      <div className="w-full overflow-x-auto">
        <table className="w-full border-collapse border border-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200 cursor-pointer hover:bg-gray-100"
                >
                  {column.label}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.map((item) => (
              <tr key={item.id}>
                {columns.map((column) => (
                  <td
                    key={`${item.id}-${column.key}`}
                    className="px-6 py-4 whitespace-nowrap"
                  >
                    {item[column.key]}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>

        {/* Pagination */}
        {pagination && (
          <div className="mt-4 flex justify-between items-center">
            <div className="flex space-x-2">
              <button
                onClick={() => onPageChange?.(currentPage - 1)}
                disabled={currentPage <= 1}
                className="px-3 py-1 border rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <FiChevronLeft className="inline-block" />
              </button>
              <span className="px-3 py-1 text-gray-500">
                Pagina {currentPage} van {totalPages}
              </span>
              <button
                onClick={() => onPageChange?.(currentPage + 1)}
                disabled={currentPage >= totalPages}
                className="px-3 py-1 border rounded-md hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <FiChevronRight className="inline-block" />
              </button>
            </div>
          </div>
        )}
      </div>
    );
  }
);

Table.displayName = "Table";

// Sample data en columns voor demonstratie
export const sampleData: TableData[] = [
  { id: 1, name: "Gebruiker 1", email: "<EMAIL>", status: "Actief" },
  {
    id: 2,
    name: "Gebruiker 2",
    email: "<EMAIL>",
    status: "Inactief",
  },
  { id: 3, name: "Gebruiker 3", email: "<EMAIL>", status: "Actief" },
];

export const sampleColumns: TableColumn[] = [
  { key: "id", label: "ID" },
  { key: "name", label: "Naam" },
  { key: "email", label: "E-mail" },
  { key: "status", label: "Status" },
];
