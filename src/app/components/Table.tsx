import { FiChevronLeft, FiChevronRight } from "react-icons/fi";

interface TableData {
  id: number;
  name: string;
  email: string;
  status: string;
}

interface TableColumn {
  key: keyof TableData;
  label: string;
}

export const Table = ({
  data,
  columns,
  pagination,
}: {
  data: TableData[];
  columns: TableColumn[];
  pagination?: boolean;
}) => {
  return (
    <div className="w-full overflow-x-auto">
      <table className="w-full border-collapse border border-gray-200">
        <thead className="bg-gray-50">
          <tr>
            {columns.map((column, index: number) => (
              <th
                key={index}
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200 cursor-pointer hover:bg-gray-100"
              >
                {column.label}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {data.map((item, index: number) => (
            <tr key={index}>
              {columns.map((column) => (
                <td
                  key={`${index}-${column.key}`}
                  className="px-6 py-4 whitespace-nowrap"
                >
                  {item[column.key]}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>

      {/* Pagination */}
      {pagination && (
        <div className="mt-4 flex justify-between items-center">
          <div className="flex space-x-2">
            <button className="px-3 py-1 border rounded-md hover:bg-gray-100">
              <FiChevronLeft className="inline-block" />
            </button>
            <span className="px-3 py-1 text-gray-500">Pagina 1 van 5</span>
            <button className="px-3 py-1 border rounded-md hover:bg-gray-100">
              <FiChevronRight className="inline-block" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

// Sample data en columns voor demonstratie
export const sampleData: TableData[] = [
  { id: 1, name: "Gebruiker 1", email: "<EMAIL>", status: "Actief" },
  {
    id: 2,
    name: "Gebruiker 2",
    email: "<EMAIL>",
    status: "Inactief",
  },
  { id: 3, name: "Gebruiker 3", email: "<EMAIL>", status: "Actief" },
];

export const sampleColumns: TableColumn[] = [
  { key: "id", label: "ID" },
  { key: "name", label: "Naam" },
  { key: "email", label: "E-mail" },
  { key: "status", label: "Status" },
];
