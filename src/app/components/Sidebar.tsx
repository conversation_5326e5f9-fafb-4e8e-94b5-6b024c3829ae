import { FiHome, FiUsers, FiBox, FiSettings, FiMenu } from "react-icons/fi";

export const Sidebar = () => {
  return (
    <div className="bg-gray-800 min-h-screen w-64 fixed left-0 top-0">
      {/* Sidebar header */}
      <div className="p-4">
        <h1 className="text-white text-xl font-bold">Admin Panel</h1>
      </div>

      {/* Navigation items */}
      <nav className="mt-4">
        <ul className="space-y-2">
          <li>
            <a
              href="#"
              className="flex items-center space-x-2 p-2 hover:bg-gray-700 rounded-lg"
            >
              <FiHome className="text-white" />
              <span className="text-white">Dashboard</span>
            </a>
          </li>
          <li>
            <a
              href="#"
              className="flex items-center space-x-2 p-2 hover:bg-gray-700 rounded-lg"
            >
              <FiUsers className="text-white" />
              <span className="text-white">Gebruikers</span>
            </a>
          </li>
          <li>
            <a
              href="#"
              className="flex items-center space-x-2 p-2 hover:bg-gray-700 rounded-lg"
            >
              <FiBox className="text-white" />
              <span className="text-white">Content</span>
            </a>
          </li>
          <li>
            <a
              href="#"
              className="flex items-center space-x-2 p-2 hover:bg-gray-700 rounded-lg"
            >
              <FiSettings className="text-white" />
              <span className="text-white">Instellingen</span>
            </a>
          </li>
        </ul>
      </nav>
    </div>
  );
};
