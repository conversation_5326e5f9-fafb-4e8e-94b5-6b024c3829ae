import { FiHome, FiUsers, FiBox, FiSettings } from "react-icons/fi";
import Link from "next/link";

export const Sidebar = () => {
  return (
    <aside
      className="bg-gray-800 min-h-screen w-64 flex-shrink-0"
      role="complementary"
      aria-label="Sidebar navigation"
    >
      {/* Sidebar header */}
      <div className="p-4">
        <h1 className="text-white text-xl font-bold">Admin Panel</h1>
      </div>

      {/* Navigation items */}
      <nav className="mt-4" role="navigation" aria-label="Main navigation">
        <ul className="space-y-2">
          <li>
            <Link
              href="/"
              className="flex items-center space-x-2 p-2 hover:bg-gray-700 rounded-lg transition-colors duration-200"
            >
              <FiHome className="text-white" aria-hidden="true" />
              <span className="text-white">Dashboard</span>
            </Link>
          </li>
          <li>
            <Link
              href="/users"
              className="flex items-center space-x-2 p-2 hover:bg-gray-700 rounded-lg transition-colors duration-200"
            >
              <FiUsers className="text-white" aria-hidden="true" />
              <span className="text-white">Gebruikers</span>
            </Link>
          </li>
          <li>
            <Link
              href="/content"
              className="flex items-center space-x-2 p-2 hover:bg-gray-700 rounded-lg transition-colors duration-200"
            >
              <FiBox className="text-white" aria-hidden="true" />
              <span className="text-white">Content</span>
            </Link>
          </li>
          <li>
            <Link
              href="/settings"
              className="flex items-center space-x-2 p-2 hover:bg-gray-700 rounded-lg transition-colors duration-200"
            >
              <FiSettings className="text-white" aria-hidden="true" />
              <span className="text-white">Instellingen</span>
            </Link>
          </li>
        </ul>
      </nav>
    </aside>
  );
};
