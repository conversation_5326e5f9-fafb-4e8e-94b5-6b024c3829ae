import { FiUsers, FiBox, FiDollarSign, FiActivity } from "react-icons/fi";

interface Metric {
  title: string;
  value: string;
  icon: React.ReactNode;
  change: string;
}

export const MetricCard = ({ title, value, icon, change }: Metric) => {
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-gray-500 text-sm font-medium">{title}</h3>
          <p className="text-2xl font-bold text-gray-900 mt-2">{value}</p>
          <span
            className={`text-sm ${
              change.startsWith("+") ? "text-green-500" : "text-red-500"
            }`}
          >
            {change} vergeleken met vorige maand
          </span>
        </div>
        <div className="bg-gray-100 p-3 rounded-full">{icon}</div>
      </div>
    </div>
  );
};

export const Dashboard = () => {
  const metrics: Metric[] = [
    {
      title: "Totaal gebruikers",
      value: "1.234",
      icon: <FiUsers className="text-blue-500" />,
      change: "+12,5%",
    },
    {
      title: "Totale omzet",
      value: "€12.345",
      icon: <FiDollarSign className="text-green-500" />,
      change: "+8,2%",
    },
    {
      title: "Actieve inhoud",
      value: "567",
      icon: <FiBox className="text-purple-500" />,
      change: "+3,1%",
    },
    {
      title: "Totale bezoeken",
      value: "23.456",
      icon: <FiActivity className="text-orange-500" />,
      change: "-2,4%",
    },
  ];

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold text-gray-900 mb-8">
        Dashboard Overzicht
      </h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric, index) => (
          <MetricCard key={index} {...metric} />
        ))}
      </div>
    </div>
  );
};
