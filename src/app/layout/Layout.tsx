import { Sidebar } from "./components/Sidebar";
import { Navbar } from "./components/Navbar";
import { Sonner } from "@/components/ui/sonner";

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <div className="flex h-screen bg-gray-100">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        <main className="p-4 flex-1">{children}</main>
      </div>
    </div>
  );
}
